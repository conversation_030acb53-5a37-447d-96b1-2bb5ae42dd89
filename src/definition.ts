/**
 * The base interface that every game module should implement.
 * It is matter of game module to generate new games per request or use singleton game instance.
 */
import { GameExtensionModule } from "./extension";

export interface GameModule<IREQ extends GameInitRequest, PREQ extends GamePlayRequest, SET, CTX extends GameContext> {

    createGame(): Game<IREQ, PREQ, SET, CTX>;
}

/**
 * The base interface that every game should implement.
 *
 * It has two main methods: initialization and process play request.
 */
export interface Game<IREQ extends GameInitRequest, PREQ extends GamePlayRequest, SET, CTX extends GameContext> {
    /**
     * Initialize game.
     *
     * The game can use GameFlow to receive request and settings.
     *
     * @param flow game flow
     */
    init(flow: GameFlow<IREQ, SET, CTX>): Promise<GameInitResponse>;

    /**
     *  Process play request.
     *
     *  The game use the field BaseRequest.request to determine the type of request.
     *  It can use GameFlow to make payment, to store game history, to receive settings and request.
     *
     * @param flow game flow
     * @param allowCheating is engine allow to cheat throw play requests
     */
    play(flow: GameFlow<PREQ, SET, CTX>, allowCheating?: boolean): Promise<GamePlayResponse | LiveGamePlayResponse>;

    /**
     *  This method is invoked in case if the game hase scheduled an internal event through
     *  PushService.scheduleInternalEvent
     *
     * @param flow game flow
     */
    internalEvent?(flow: GameFlow<PREQ, SET, CTX>): Promise<GamePlayResponse | LiveGamePlayResponse>;

    /**
     *  This method will help to finalize the game after player disconnected event.
     *
     * @param flow game flow
     */
    disconnect?(flow: GameFlow<BaseRequest, SET, CTX>): Promise<void>;

    /**
     *  Returns extension points of the game
     *
     * @returns {GameExtensionModule[]}
     */
    getExtensionModules?(): GameExtensionModule[];

    /**
     * Get information of the current game state
     *
     */
    stateInfo?(flow: GameFlow<GameStateInfoRequest, SET, CTX>): Promise<GameStateInfoResponse>;

    /**
     * Get information about the current game round in bonus feature mode.
     * @param flow
     */
    isBonusFeatureMode?(flow: GameFlow<undefined, SET, CTX>): Promise<boolean>;

    /**
     * If game is expired/finalized engine will invoke this method to get the new game context value
     * The Game is responsible to remove "unfinished" rounds information from the game context  and return the result.
     */
    finalizeGame?(flow: GameFlow<undefined, SET, CTX>): Promise<ContextUpdate<CTX>>;
}

/**
 * How long to store game context information.
 * NORMAL - context will be removed if the game isn't brokn
 * LONG_TERM - store context for long period of time. The actual longevity  is determined by game engine.
 * SHOW_TERM - store context for short period of time. The actual longevity  is determined by game engine.
 * BROKEN - If we couldn't relay upon roundEnded to mark the game as broken.
 */
export enum GameContextPersistencePolicy {
    NORMAL = 0,
    SHORT_TERM = 1,
    LONG_TERM = 2,
    BROKEN = 3
}

/**
 * This the base interface that should be used by game
 * to communicate with external services like : wallet, history storage, settings&configurations,etc.
 */
export interface GameFlow<REQ extends BaseRequest, ST, CTX extends GameContext> {
    /**
     * Incoming game request
     */
    request(): REQ;
    /**
     * Game identifier
     */
    info(): GameFlowInfo;
    /**
     * Random number generator
     */
    rng(): RandomGenerator;

    /**
     *  Different game settings, limits.
     */
    settings(): ST;

    /**
     * Push service which can be used for player notifications.
     *
     * It's supported only for the games with bidirectional transport communication
     */
    pushService(): PushService;

    /**
     * Get current game context
     */
    gameContext(): Promise<CTX>;

    /**
     * Context persistence policy
     */
    persistencePolicy(): GameContextPersistencePolicy;

    /**
     * Set game context persistence policy.
     *
     * The policy will be applied only after any of update context methods.
     * If the Game decides to use the policy different of NORMAL, it is  responsible for reset this value later.
     * Game engine doesn't reset it after re-init or other actions.
     */
    setPersistencePolicy(value: GameContextPersistencePolicy): void;

    /**
     * This method returns player balance from wallet system
     *
     * If the game use coins for play, it should provide coinsRate value for conversion
     *
     * @param coinsRate coinsRate
     */
    getBalance(): Promise<BalanceResponse>;

    /**
     * Store payment information
     *
     * @param payment store payment information
     */
    payment(payment: PaymentInfo): Promise<BalanceResponse>;

    /**
     * Stores history element
     *
     * @param type - type of history element
     * @param version - version
     * @param history - history element
     */
    storeHistory(history: GameHistory): Promise<void>;

    /**
     *
     * Updates game context
     *
     * @param gameContext current game context
     */
    updateGameContext(gameContext: CTX): Promise<CTX>;

    /**
     * Makes atomic update: update game state, save history, make wallet operation, contributes to JPN
     *
     * @param gameContext
     * @param paymentInfo
     * @param history
     * @param jackpotAction
     * @param analytics
     */
    update(gameContext: CTX,
           payment: PaymentInfo,
           history: GameHistory,
           jackpotAction?: JackpotAction,
           analytics?: Analytics): Promise<void>;

    /**
     * Updates Jackpot mini game status.
     *
     * @param gameContext
     * @param miniGame
     * @param analytics
     */
    updateMiniGame(gameContext: CTX, miniGame: MiniGame, analytics?: Analytics): Promise<void>;

    /**
     * Log game information
     * @param level - log level
     * @param message - message
     * @param optionalParams - optional params, style strfmt output
     */
    log(level: LogLevel, message: any, ...optionalParams: any[]): void;

    /**
     * Exchange money from baseCurrency to targetCurrency
     *
     * The parameter baseCurrency is optional, default value is the player currency
     */
    exchange(amount: number, targetCurrency: string, baseCurrency?: string): number;

    /**
     * Perform jackpot win from defined jackpot, update game context and save history.
     *
     */
    winJackpot(gameContext: CTX, winJackpot: WinJackpot, history: GameHistory, analytics?: Analytics): Promise<void>;

    /**
     *  Gets free bets information
     */
    getFreeBets(info: GameInfo): Promise<FreeBetsInfo>;

    /**
     * Gets current values of jackpot tickers in player's currency.
     */
    jackpotTickers(): Promise<TickersResponse>;

    /**
     * Gets the last jackpot win result.
     */
    jackpotResult(): Promise<JackpotResults>;

    /**
     * Information that will be used for update after finishing request processing.
     *
     */
    deferredUpdate(update: ContextUpdate<CTX>): void;
}

export interface GameInfo {
    /**
     * Coin multiplier used to calculate total bet value (e.g. count of lines in slot games)
     */
    totalBetMultiplier: number;
    /**
     * Set of possible bet values in currency.
     */
    stakeAll: number[];
    /**
     * Do not throw error when promo's coin is not in stakeAll array
     */
    skipCoinValidation?: boolean;
}

export interface FreeBetsInfo {

    amount: number;

    coin: number;
}

export interface ContextUpdate<CTX extends GameContext> {

    payment?: PaymentInfo;

    history?: GameHistory;

    context?: CTX;

    jackpotAction?: JackpotAction;

    analytics?: Analytics;

    /**
     * this param uses for stage flow only
     * this parameter allows you to modify the main game context from another flow
     */
    gameContext?: CTX;
}

export interface Analytics {
    /**
     * The type  of analytics data.
     * This specify the topic where we put the data.*
     */
    type: string;
    /**
     * The Data, that will be published into analytical topic.
     */
    data: any;
}

/**
 *  Conversion of amount from & to some game currencies, for instance coins.
 *
 *   If a Game want to use custom currencies, like coins, it has to implement this interface
 *
 *
 *   Example:
 *
 *   class GameImpl implements Game, ConversionService{
 *
 *    }
 *
 *
 *  The methods of this interface will be used for converting balance and payment information inside game engine.
 *  The game desn't have to use this methods directly, just implement conversion.
 */
export interface ConversionService<SET> {
    /**
     *  convert real money to game currency,
     *
     *  convert game money to game currency
     *
     * @param amount
     */
    toGameAmount(gameInfo: GameFlowInfo, settings: SET, amount: number): number;


    /**
     * Convert game amount  to currency
     * @param gameInfo
     * @param settings
     * @param amount
     */
    fromGameAmount(gameInfo: GameFlowInfo, settings: SET, amount: number): number;
}

/**
 * Game with jackpots.
 */
export interface JackpotGame<SET, CTX> {

    /**
     * Get current value of jackpot tickers in player's currency.
     */
    getJackpotTickers(flow: GameFlow<BaseRequest, SET, CTX>): Promise<TickersResponse>;

    /**
     * Get last jackpot win result.
     */
    getJackpotResult(flow: GameFlow<BaseRequest, SET, CTX>): Promise<JackpotResults>;
}

/**
 * Push service which can be used for player notifications.
 *
 * It's supported only for the games with bidirectional transport communication
 */
export interface PushService {
    /**
     * Sends notification to client
     */
    notifyPlayer(msg: GameEvent): Promise<void>;
    /**
     * Sends error to the client
     */
    notifyError(err: Error): Promise<void>;

    /**
     * Schedule internal event for execution
     *
     * This method will put the request to processing queue.
     * During processing special method Game.internalEvel will be incoked
     *
     */
    scheduleInternalEvent(request: GamePlayRequest): void;
}

export interface GameEvent {
    type: string;
}

export type SomeGameModule = GameModule<GameInitRequest, GamePlayRequest, any, GameContext>;

export type SomeGame = Game<GameInitRequest, GamePlayRequest, any, GameContext>;

export interface SomeGameFlow<REQ extends BaseRequest> extends GameFlow<REQ, any, GameContext> {
}

/**
 * Game specific structure that contains game state
 *
 */
export interface GameContext {
}

/**
 *  Random numer generator
 */
export interface RandomGenerator {
    /**
     * Generate next random number
     * @param max max falue
     */
    get(max?: number): number;

    /**
     * random number in some boundary
     * @param min min value
     * @param max max value
     */
    range(min: number, max: number): number;

    getRandomValueWithProbability(pairs: ValueProbabilityPairs): any;

    getRandomNumberWithProbability(pairs: ValueProbabilityPairs): number;

    getRandomValueWithWeight(pairs: ValueWeightPairs): any;

    /**
     * Generates next random number and compares probabilities
     * @param p probability value
     */
    checkProbability(p: number): boolean;
}

/**
 * Some game request.
 * It's game custom structure.
 * Each kind of have should implement this interface and add game specific fields
 */
export interface BaseRequest {
    request: string;
    requestId?: number;
    gameSession?: string;
}

/**
 * Init game request
 */
export interface GameInitRequest extends BaseRequest {
    request: 'init';
    name: string;
    gameId: string;
    deviceId: string;
    startGameToken: string | FunStartGameToken;
}

export type TransferOperationType = "transfer-in" | "transfer-out";

/**
 * Balance transfer request. Game controller is responsible for handling the request.
 */
export interface BalanceTransferRequest extends BaseRequest {
    request: TransferOperationType;
    amount: number;
}

/**
 * Request current value of Jackpot.
 */
export interface JackpotTickerRequest extends BaseRequest {
    request: "jp-ticker";
    getBalance?: boolean;
}

/**
 * Request to start Jackpot mini game.
 */
export interface StartMiniGameRequest extends GamePlayRequest {
    request: "start-mini-game";
}

/**
 * Request to play Jackpot mini game.
 */
export interface PlayMiniGameRequest extends GamePlayRequest {
    request: "play-mini-game";
}

/**
 * Free bet request.
 */
export interface FreeBetRequest extends GamePlayRequest {
    bet?: number;
    freeBet: boolean;
}

export interface FunStartGameToken {
    playerCode: string;
    gameCode: string;
    brandId: number;
    currency: string;
    module?: string;
}

/**
 * Game play request.
 *
 * Game can process different kinds of play request types.
 * The field GamePlayRequest.request can be game specific (spin, fire, etc.)
 */
export interface GamePlayRequest extends BaseRequest {
}

export interface GameStateInfoRequest extends BaseRequest {
    request: "state";

    /**
     * If we need to return balance
     */
    getBalance?: boolean;
    /**
     * If we need to return tickers
     */
    getTickers?: boolean;

    /**
     * Any game-special description of what we need to return
     */
    [field: string]: any;
}

export interface GameStateInfoResponse extends BaseResult {
    [field: string]: any;
}

export interface BaseResult {
    request: string;
}

/**
 * Game init response
 *
 * It's game custom structure.
 * Each kind of have should implement this interface and add game specific fields
 */
export interface GameInitResponse extends BaseResult {
}

/**
 * Response on game play request.
 * If play request was a bet action. this will contains win/bet information
 */

export interface GamePlayResponse extends BaseResult {
    totalBet?: Currency;
    totalWin?: Currency;
    roundEnded?: boolean;
}

export interface LiveGamePlayResponse {
    response: GamePlayResponse;
    dontUseBalance?: boolean; // using getBalance is enabled by default
}
/**
 * Game specific history information
 */
export interface GameHistory {
    type: string;
    roundEnded: boolean;
    separateRound?: boolean;
    data: any;
    isHidden?: boolean;
}

/**
 * Jurisdiction settings to apply to game
 */
export interface JurisdictionSettings {
}

/**
 * Game settings (features) to apply to game
 */
export interface GameSettings {
}

/**
 * Brand settings to apply to game
 */
export interface BrandSettings {
}

export type GameMode = "real" | "fun" | "bns" | "play_money";

/**
 *  Current game information
 */
export interface GameFlowInfo {
    readonly gameId: string;
    readonly gameCode: string;
    readonly deviceId: string;
    readonly brandId: number;
    readonly playerCode: string;
    readonly currency: string;
    readonly gameSessionId: string;
    readonly roundId: string;
    readonly roundPID: string;
    readonly gameMode: GameMode;
    readonly gameToken?: string;
    readonly module?: string;
    readonly jrsdSettings?: JurisdictionSettings;
    readonly brandSettings?: BrandSettings;
    readonly gameSettings?: GameSettings;
    readonly jurisdictionCode?: string;
    readonly isTestPlayer?: boolean;
}

/**
 * Payment Information.
 *
 * Specific game can extend this interface.
 * It will allow to integrate with different wallet implementation
 */
export interface PaymentInfo {
    bet?: number;
    win?: number;
    actions?: GameWalletAction[];

    [field: string]: any;
}

/**
 * Type of wallet operation: increment or decrement
 */
export type GameWalletActionType = "debit" | "credit";

/**
 * Wallet operation
 */
export interface GameWalletAction {
    /**
     * Wallet operation type
     */
    action: GameWalletActionType;
    /**
     * Account attribute : balance, xp, diamond,etc.
     */
    attribute: string;
    /**
     * Amount
     */
    amount: number;

    /**
     * The change type, optional.
     */
    changeType?: string;

    [field: string]: any;
}

export interface JackpotAction {
    type: string;
}

export interface CheckJackpotWin extends JackpotAction {
    type: "checkWin";
    /**
     * If we want to check win just for particular jackpots
     */
    jackpotIds?: string[];

    [field: string]: any;
}

export interface Contribution extends JackpotAction {
    type: "contribution";
    /**
     * If we want to contribute just for particular jackpots
     */
    jackpotIds?: string[];
    amount: number;

    [field: string]: any;
}

export interface MiniGame extends JackpotAction {
    type: "mini-game";
    [field: string]: any;
}

export interface ValueProbabilityPair {
    value: any;
    probability: number;
}

export type ValueWeightPairs = Array<ValueWeightPair>;

export interface ValueWeightPair {
    value: any;
    weight: number;
}

export type ValueProbabilityPairs = Array<ValueProbabilityPair>;

export type Currency = number;

export interface ClientRequest {
    request: GameInitRequest|GamePlayRequest;
}

export interface BalanceAccountResponse {
    amount: Currency;
}

export interface Balances {
    [accountName: string]: BalanceAccountResponse;
}

export interface BonusCoinsBalance {
    amount: number;
    rewardedAmount: number;
    redeemMinAmount: number;
    redeemMaxAmount?: number;
    redeemBalance?: number;
    redeemCurrency?: string;
    expireAt: string;
    expireCountdown: number;
    promoId: string;
    externalId?: string;
}

export interface ExtraData {
    [field: string]: any;
}

export interface BalanceResponse {
    currency: string;
    amount: Currency;
    real: BalanceAccountResponse;
    bonus: BalanceAccountResponse;
    external?: BalanceAccountResponse;
    extraBalances?: Balances;
    freeBets?: BalanceAccountResponse;
    bonusCoins?: BonusCoinsBalance;
    extraData?: ExtraData;
}

export type TickersResponse = TickerResponse[];

export interface TickerResponse {
    jackpotId: string;
    pools: JackpotPoolsInformation;
}

export interface JackpotPoolInformation {
    amount: number;
}

export interface JackpotPoolsInformation {
    [field: string]: JackpotPoolInformation;
}

export interface JackpotResult {
    jackpotId: string;
    event: string;
    [field: string]: any;
}

export interface StartMiniGameResult extends JackpotResult {
    event: "start-mini-game";
}

export interface JackpotWinResult extends JackpotResult {
    event: "win";
    amount: number;
    pool?: string;
}

export type JackpotResults = JackpotResult | JackpotResult[];

export interface ClientSettings {
    nickname: string;
    ivp?: boolean;
    sPblkCtBk?: boolean;
    sPrlkCtBk?: boolean;
    hsWrn?: boolean;
    niChAttLt?: number; // nicknameChangeAttemptsLeft
}

export interface ClientResponse {
    gameSession: string;
    balance: BalanceResponse;
    result: BaseResult;
    /**
     * The same requestId that was sent with correspondent request
     */
    requestId?: number;
    // Contains the first element of 'tickers' field. Need it for backward compatibility with OMQ and FuFish.
    ticker?: TickerResponse;
    tickers?: TickersResponse;
    jackpot?: JackpotResults;
    jrsdSettings?: JurisdictionSettings;
    jurisdictionCode?: string;
    playedFromCountry?: string;
    roundEnded?: boolean;
    extraData?: ExtraData;
    clientSettings?: ClientSettings;
    roundTotalBet?: number;
    roundTotalWin?: number;
}

export interface WinJackpot extends JackpotAction {
    type: "win-jackpot";
    jackpotId: string;
    /**
     * If the game specifies the win amount.
     */
    amount?: number;
    [field: string]: any;
}

/**
 * Log level for a logger.
 */
export const enum LogLevel {
    Debug,
    Info,
    Warn,
    Error,
}

export * from "@skywind-group/sw-random-cs";
