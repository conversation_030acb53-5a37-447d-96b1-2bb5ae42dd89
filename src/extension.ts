import {
    ContextUpdate,
    GameContext,
    GameInitRequest,
    GameInitResponse,
    GamePlayRequest,
    GamePlayResponse,
    GameStateInfoRequest,
    GameStateInfoResponse,
    SomeGame,
    SomeGameFlow
} from "./definition";

/**
 * Extension module that can be executed
 */
export interface GameExtensionModule {

    /**
     * Invoked before execution of Game.init
     */
    beforeInit(game: SomeGame, flow: SomeGameFlow<GameInitRequest>): Promise<void | GameInitResponse>;

    /**
     * Invoked after execution of Game.init
     */
    afterInit(game: SomeGame, flow: SomeGameFlow<GameInitRequest>,
              response: GameInitResponse,
              update?: ContextUpdate<GameContext>): Promise<void | GameInitResponse>;

    /**
     * Invoked before execution of Game.play
     */
    beforePlay(game: SomeGame, flow: SomeGameFlow<GamePlayRequest>): Promise<void | GamePlayResponse>;

    /**
     * Invoked after execution of Game.play
     */
    afterPlay(game: SomeGame,
              flow: SomeGameFlow<GamePlayRequest>,
              response: GamePlayResponse,
              update?: ContextUpdate<GameContext>): Promise<void | GamePlayResponse>;

    /**
     * Invoked before execution of Game.stateInfo
     */
    beforeStateInfo(game: SomeGame, flow: SomeGameFlow<GameStateInfoRequest>): Promise<void | GameStateInfoResponse>;

    /**
     * Invoked after execution of Game.stateInfo
     */
    afterStateInfo(game: SomeGame,
                   flow: SomeGameFlow<GameStateInfoRequest>,
                   response: GameStateInfoResponse): Promise<void | GameStateInfoResponse>;

}
