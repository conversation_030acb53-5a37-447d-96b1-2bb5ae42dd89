import {
    PaymentInfo,
    GameHistory,
    <PERSON><PERSON>Action
} from "../../definition";

import {
    WrongRequest,
    LimitsNotValid
} from "./errors";

import {
    BaseGameActionMetricsHandler,
    BaseGameMetricsManager,
    StatsInitGame,
    BaseGameMetricsManagerImpl,
    BaseGamePlayStatsResult,
    StatsProcessPlayResult
} from "./statistics/index";

import {
    BaseGame,
    BaseGameConfig,
    BaseGameContext,
    BaseGameInitFlow,
    BaseGameInitResponse,
    BaseGameAction,
    BaseGamePlayFlow,
    BaseGamePlayResponse,
    BaseGameResponseAdapter,
    BaseGameContextAdapter,
    BaseGameStatsAdapter,
    BaseGameActionValidator
} from "./definitions";

import * as _ from "lodash";

/**
 * Bare minimum that is required in init response, most of games
 * will return something more
 */
export class DefaultBaseGameResponseAdapter implements BaseGameResponseAdapter {
    createInitResponse(game: BaseGame, flow: BaseGameInitFlow, context: BaseGameContext): BaseGameInitResponse {
        return <BaseGameInitResponse>{
            request       : flow.request().request,
            gameId        : game.gameId,
            version       : game.version,
            name          : flow.request().name,
            settings      : flow.settings(),
            previousResult: context.previousResult
        };
    }
}

export class DefaultBaseGameContextAdapter implements BaseGameContextAdapter {
    async fillInitialContext(flow: BaseGameInitFlow, context: BaseGameContext): Promise<BaseGameContext> {
        return context;
    }

    cleanUpContext(context: BaseGameContext) {}
}

/**
 * right now games that use vanilla game are omq and card games
 * their state validation is different between them
 * omq has validation in each action but card games have validation
 * at top level game that checks same context properties for all actions
 *
 * this default covers omq but can be replaced by another that works as
 * required by card games
 */
export class DefaultBaseGameActionValidator implements BaseGameActionValidator {
    async validateAction(flow: BaseGameInitFlow, context: BaseGameContext,
        requestType: string, action: BaseGameAction): Promise<void> {

        if (action.validateState !== undefined) {
            await action.validateState(flow, flow.request(), context);
        }
    }
}

export class DefaultBaseGameStatsAdapter implements BaseGameStatsAdapter {
    transformPlayResult(result: BaseGamePlayResponse): BaseGamePlayStatsResult {
        return <BaseGamePlayStatsResult>_.cloneDeep(result);
    }
}

/**
 * This would be class that game would extend to add their actual functionality
 */
export abstract class BaseGameActionImpl implements BaseGameAction {
    constructor(readonly actionName: string) {}

    async process(game: BaseGame, flow: BaseGamePlayFlow, context: BaseGameContext,
        allowCheating?: boolean): Promise<BaseGamePlayResponse> {

        const result = await this.play(game, flow, context, allowCheating);

        // engine's gameflow checks for undefined values before acting on
        // supplied objects so supplying undefined values here is safe
        await flow.update(context,
            this.createPayment(context, result),
            this.createHistory(context, result),
            this.createJackpotAction(context, result)
        );

        return result;
    }

    createPayment(context: BaseGameContext, result: BaseGamePlayResponse): PaymentInfo {
        return undefined;
    }

    createHistory(context: BaseGameContext, result: BaseGamePlayResponse): GameHistory {
        return undefined;
    }

    createJackpotAction(context: BaseGameContext, result: BaseGamePlayResponse): JackpotAction {
        return undefined;
    }

    registerMetrics(manager: BaseGameMetricsManager) {
        const handler = new BaseGameActionMetricsHandler(this.actionName, this.actionName, manager);
        manager.registerActionMetricHandler(handler);
    }

    abstract play(game: BaseGame, flow: BaseGamePlayFlow, context: BaseGameContext,
        allowCheating?: boolean): Promise<BaseGamePlayResponse>;
}

export class BaseGameImpl implements BaseGame {
    public statsManager: BaseGameMetricsManager;

    constructor(readonly config: BaseGameConfig) {
        this.initGame(config);
    }

    @StatsInitGame()
    initGame(config: BaseGameConfig) {
        // default adapters are added as they have somewhat sensible default behavior
        // they should be replaced as needed
        this.config.contextAdapter  = this.config.contextAdapter  || new DefaultBaseGameContextAdapter();
        this.config.actionValidator = this.config.actionValidator || new DefaultBaseGameActionValidator();
        this.config.responseAdapter = this.config.responseAdapter || new DefaultBaseGameResponseAdapter();
        this.config.statsAdapter    = this.config.statsAdapter    || new DefaultBaseGameStatsAdapter();
    }

    async init(flow: BaseGameInitFlow): Promise<BaseGameInitResponse> {
        const req = flow.request();

        if (req.request !== "init") {
            return Promise.reject(new WrongRequest());
        }

        if (!this.validLimits(flow.settings())) {
            return Promise.reject(new LimitsNotValid());
        }

        const context = await this.config.contextAdapter
            .fillInitialContext(flow, <BaseGameContext>(await flow.gameContext()));

        await flow.updateGameContext(context);

        return this.config.responseAdapter
            .createInitResponse(this, flow, context);
     }

    @StatsProcessPlayResult()
    async play(flow: BaseGamePlayFlow, allowCheating?: boolean): Promise<BaseGamePlayResponse> {
        const request = flow.request();
        const action = this.config.gameActions[request.request];
        const context = await flow.gameContext();

        if (!this.config.gameActions.hasOwnProperty(request.request)) {
            return Promise.reject(new WrongRequest());
        }

        try {
            await this.config.actionValidator
                .validateAction(flow, context, request.request, action);
        } catch (e) {
            this.config.contextAdapter.cleanUpContext(context);
            await flow.updateGameContext(context);
            return Promise.reject(e);
        }

        return action.process(this, flow, context, allowCheating);
    }

    protected validLimits(settings: any): boolean {
        return this.config.requiredSettings
            .every(s => settings[s] !== undefined);
    }

    get version(): string {
        try {
            return require("../../../../../../package.json").version;
        } catch (e) {
            return undefined;
        }
    }

    get gameId(): string {
        return this.config.gameId;
    }

    getStatsMetricsManager() {
        return new BaseGameMetricsManagerImpl(this);
    }
}
