import {
    Game,
    GameInitRequest,
    GamePlayRequest,
    GameContext,
    GameFlow,
    GameInitResponse,
    GamePlayResponse,
    PaymentInfo,
    GameHistory,
    JackpotAction
} from "../../definition";

import {
    BaseGameMetricsManager,
    BaseGamePlayStatsResult
} from "./statistics/index";

export type BaseGameSettings = any;

// flow is kinda a god object that contains anything that might be useful
// as a result to make things easier everything receives it as an argument
export interface BaseGameInitFlow extends GameFlow<BaseGameInitRequest, BaseGameSettings, BaseGameContext> {}
export interface BaseGamePlayFlow extends GameFlow<BaseGamePlayRequest, BaseGameSettings, BaseGameContext> {}
export interface BaseGamePlayRequest extends GamePlayRequest {}
export interface BaseGameInitRequest extends GameInitRequest {}
export interface BaseGamePlayResponse extends GamePlayResponse {}

// @XXX generic dictionary type?
export interface BaseGameActions {
    [field: string]: BaseGameAction;
}

export interface BaseGameConfig {
    gameId: string;
    /**
     * For validating that all required settings(most notably limits) have
     * been specified in game settings
     */
    requiredSettings: Array<string>;
    /**
     * For changing response that will be sent back to client
     * as it might contain additional fields from context etc.
     */
    responseAdapter?: BaseGameResponseAdapter;
    /**
     * For changing context after it gets initialized
     */
    contextAdapter?: BaseGameContextAdapter;
    /**
     * Called when action is about to be called, can be used to
     * validate overall context or single action's state
     */
    actionValidator?: BaseGameActionValidator;
    /**
     * For copying results from play response to stats result if result
     * handler requires custom fields etc.
     */
    statsAdapter?: BaseGameStatsAdapter;
    gameActions: BaseGameActions;
}

export interface BaseGameInitResponse extends GameInitResponse {
    name: string;
    gameId: string;
    settings: any;
    previousResult: GamePlayResponse;
    version?: string;
}

export interface BaseGame extends Game<BaseGameInitRequest, BaseGamePlayRequest, any, BaseGameContext> {
    init(flow: BaseGameInitFlow): Promise<GameInitResponse>;
    play(flow: BaseGamePlayFlow, allowCheating?: boolean): Promise<GamePlayResponse>;
    version: string;
    gameId: string;
    config: BaseGameConfig;
    statsManager: BaseGameMetricsManager;
    getStatsMetricsManager(): BaseGameMetricsManager;
}

/**
 * Every game puts its own things in the context
 */
export interface BaseGameContext extends GameContext {
    previousResult: GamePlayResponse;
}

/**
 * Different games send different things in response
 */
export interface BaseGameResponseAdapter {
    createInitResponse(game: BaseGame, flow: BaseGameInitFlow, context: BaseGameContext): BaseGameInitResponse;
}

/**
 * Context usually requires some things to be put in context, but those things are
 * game specific
 *
 * Cleanup can be done after validation fails
 */
export interface BaseGameContextAdapter {
    fillInitialContext(flow: BaseGameInitFlow, context: BaseGameContext): Promise<BaseGameContext>;
    cleanUpContext(context: BaseGameContext);
}


export interface BaseGameStatsAdapter {
    /**
     * Should copy required values from response result to
     * stats result so that any changes that are done in stats handler
     * suddenly don't change values somewhere else
     *
     * @param  {BaseGamePlayResponse}             result
     * @return {Promise<BaseGamePlayStatsResult>}
     */
    transformPlayResult(result: BaseGamePlayResponse): BaseGamePlayStatsResult;
}

export interface BaseGameActionValidator {
    /**
     * Can perform overall game state check as well as individual
     * game action state
     *
     * @throws {Error} Throw error if someting isn't as expected
     */
    validateAction(flow: BaseGamePlayFlow, context: BaseGameContext,
        requestType: string, action: BaseGameAction): Promise<void>;
}

export interface BaseGameAction {
    actionName: string;
    process(game: BaseGame, flow: BaseGamePlayFlow, context: BaseGameContext,
        allowCheating?: boolean): Promise<BaseGamePlayResponse>;

    validateState?(flow: BaseGamePlayFlow, request: BaseGamePlayRequest,
        context: BaseGameContext): Promise<void>;
    createPayment(context: GameContext, result: BaseGamePlayResponse): PaymentInfo;
    createHistory(context: GameContext, result: BaseGamePlayResponse): GameHistory;
    createJackpotAction(context: GameContext, result: BaseGamePlayResponse): JackpotAction;

    registerMetrics(manager: BaseGameMetricsManager);
}
