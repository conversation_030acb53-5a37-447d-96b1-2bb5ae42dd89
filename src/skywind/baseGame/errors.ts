// @XXX maybe have common error package that includes
// most commonly used errors?

export class SW<PERSON>rror extends Error {
    status: number = 400;
    code: number = 0;
    message: string;

    constructor(code: number, message: string) {
        super(message);
        this.message = message;
        this.code = code;
    }
}
export class WrongRequest extends SWError {
    constructor() {
        super(100, 'Wrong request');
    }
}

export class LimitsNotValid extends SWError {
    constructor() {
        super(400, "Limits are not valid");
    }
}