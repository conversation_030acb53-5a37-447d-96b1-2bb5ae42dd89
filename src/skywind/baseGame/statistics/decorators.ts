import {
    BaseGame,
    BaseGamePlayFlow
} from "../index";

import config from "./config";

/**
 * Initializes stats manager that will be responsible for distributing result
 * between action metric handlers
 */
export function StatsInitGame() {
    return function(target: Object,
                    key: string,
                    descriptor: TypedPropertyDescriptor<any>): TypedPropertyDescriptor<any> {
        const original: Function = descriptor.value;

        descriptor.value = function(): void {
            original.apply(this, arguments);
            if (config.statistics.logStatistics) {
                (<BaseGame>this).statsManager = (<BaseGame>this).getStatsMetricsManager();
            }
        };

        return descriptor;
    };
}

/**
 * Stats are collected after game action is executed
 */
export function StatsProcessPlayResult() {
    return function (target: Object, key: string,
                     descriptor: TypedPropertyDescriptor<any>): TypedPropertyDescriptor<any> {
        const original: Function = descriptor.value;

        descriptor.value = async function (flow: BaseGamePlayFlow): Promise<void> {
            const result = await original.apply(this, arguments);

            if (config.statistics.logStatistics) {
                (<BaseGame>this).statsManager.handleResult(flow.request().request, result);
            }

            return result;
        };

        return descriptor;
    };
}