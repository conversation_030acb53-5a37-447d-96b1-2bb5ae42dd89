import {
    BaseGamePlayResponse
} from "../definitions";

export interface BaseGameStatsOutputFeature {
    id: string;
    items: Array<BaseGameStatsOutputFeatureItem>;
}

export interface BaseGameStatsOutputFeatureItem {
    title: string;
    value: any;
}

export interface BaseGameMetrics {
    rounds: number;
    triggerCount: number;
    totalWin: number;
    totalWinPow: number;
    totalBet: number;
    rtp: number;
    stdev: number;
    roundWin: number;
    winCount: number;
}

export interface BaseGamePlayStatsResult extends BaseGamePlayResponse {}

export interface BaseGameActionMetricsDictionary {
    [key: string]: BaseGameMetrics;
}

export interface BaseGameMetricsHandler {
    id: string;
    manager: BaseGameMetricsManager;

    handleMetrics(metrics: BaseGameMetrics, action: string, result: BaseGamePlayStatsResult);
    calcMetrics(metrics: BaseGameMetrics);
    aggregateMetrics(metrics: BaseGameMetrics, sourceMetrics: BaseGameMetrics);
    resetMetrics(metrics: BaseGameMetrics);
    initMetric(): BaseGameMetrics;
    getOutputFeaturesList(metrics: BaseGameMetrics): BaseGameStatsOutputFeature;
    getTotalBet(metrics: BaseGameMetrics): number;
    getTotalRoundCount(metrics: BaseGameMetrics): number;
}

/**
 * Used for containing metric handling logic in single place
 *
 * Game will create metrics manager when its InitGame decorator is called
 */
export interface BaseGameMetricsManager {
    registerActionMetricHandler(handler: BaseGameMetricsHandler);
    getHandlerMetric(handler: BaseGameMetricsHandler);
    handleResult(action: string, result: BaseGamePlayStatsResult);
    getTotalBet(): number;
    getTotalRoundCount(): number;
    getOutputFeatures(): Array<BaseGameStatsOutputFeature>;
    getRawMetrics(): BaseGameActionMetricsDictionary;
    aggregateMetrics(metrics: BaseGameActionMetricsDictionary);
    resetMetrics();
}