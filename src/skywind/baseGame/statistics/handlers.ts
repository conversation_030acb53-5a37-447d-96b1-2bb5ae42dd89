import {
    BaseGameMetrics,
    BaseGameStatsOutputFeature,
    BaseGameMetricsHandler,
    BaseGameMetricsManager,
    BaseGamePlayStatsResult
} from "./definitions";

/**
 * Simple metrics handler for summing winnings and bets
 *
 * Produces basic values expected in output(rtp, stdev, average win etc.)
 */
export abstract class AbstractBaseGameMetricsHandler implements BaseGameMetricsHandler {
    constructor(readonly id: string, readonly manager: BaseGameMetricsManager) {}

    abstract getTotalBet(metrics: BaseGameMetrics): number;

    abstract getTotalRoundCount(metrics: BaseGameMetrics): number;

    canHandleMetric(action: string, result: BaseGamePlayStatsResult): boolean {
        return true;
    }

    initMetric(): BaseGameMetrics {
        return this.getNewMetrics();
    }

    getNewMetrics(): BaseGameMetrics {
        const metrics = <BaseGameMetrics>{};
        this.resetMetrics(metrics);
        return metrics;
    }

    roundEnded(result: BaseGamePlayStatsResult): boolean {
        return !!result.roundEnded;
    }

    handleMetrics(metrics: BaseGameMetrics, action: string, result: BaseGamePlayStatsResult) {
        if (!this.canHandleMetric(action, result)) {
            return;
        }

        this.updateMetrics(metrics, result);
    }

    updateMetrics(metrics: BaseGameMetrics, result: BaseGamePlayStatsResult) {
        metrics.triggerCount += 1;

        metrics.totalWin += result.totalWin || 0;
        metrics.totalBet += result.totalBet || 0;
        metrics.roundWin += result.totalWin || 0;

        if (this.roundEnded(result)) {
            if (metrics.roundWin > 0) {
                metrics.winCount += 1;
            }

            metrics.rounds += 1;
            metrics.totalWinPow += Math.pow(metrics.roundWin, 2);
            metrics.roundWin = 0;
        }
    }

    aggregateMetrics(sinkMetric: BaseGameMetrics, sourceMetrics: BaseGameMetrics) {
        sinkMetric.rounds       += sourceMetrics.rounds;
        sinkMetric.totalWin     += sourceMetrics.totalWin;
        sinkMetric.totalWinPow  += sourceMetrics.totalWinPow;
        sinkMetric.totalBet     += sourceMetrics.totalBet;
        sinkMetric.winCount     += sourceMetrics.winCount;
        sinkMetric.triggerCount += sourceMetrics.triggerCount;
    }

    calcMetrics(metrics: BaseGameMetrics) {
        metrics.rtp = metrics.totalWin / this.getTotalBet(metrics);
        metrics.stdev = this.calcStdev(metrics.rounds, metrics.totalWin, metrics.totalWinPow);
    }

    private calcStdev(rounds: number, totalWin: number, totalWinPow: number): number {
        return Math.sqrt(
            ((totalWinPow / rounds) - Math.pow(totalWin / rounds, 2))
            * (rounds / (rounds - 1))
        );
    }

    getOutputFeaturesList(metrics: BaseGameMetrics): BaseGameStatsOutputFeature {
        this.calcMetrics(metrics);

        const statsFeature: BaseGameStatsOutputFeature = {
            id: this.id,
            items: []
        };

        statsFeature.items.push({
            title: "Rounds",
            value: metrics.rounds
        });
        statsFeature.items.push({
            title: "RTP",
            value: metrics.rtp
        });
        statsFeature.items.push({
            title: "Hit Rate",
            value: metrics.winCount / metrics.rounds
        });
        statsFeature.items.push({
            title: "Total Bet",
            value: this.getTotalBet(metrics)
        });
        statsFeature.items.push({
            title: "Total Win",
            value: metrics.totalWin
        });
        statsFeature.items.push({
            title: "Total Win POW",
            value: metrics.totalWinPow
        });
        statsFeature.items.push({
            title: "Win Count",
            value: metrics.winCount
        });
        statsFeature.items.push({
            title: "Average Win",
            value: metrics.totalWin / metrics.rounds
        });
        statsFeature.items.push({
            title : "Average Positive Win",
            value : metrics.totalWin / metrics.winCount || 0
        });
        statsFeature.items.push({
            title : "1 in X rounds",
            value : this.getTotalRoundCount(metrics) / metrics.rounds || 0
        });
        statsFeature.items.push({
            title : "Trigger count",
            value : metrics.triggerCount
        });
        statsFeature.items.push({
            title: "STDEV",
            value: metrics.stdev
        });

        return statsFeature;
    }

    resetMetrics(metrics: BaseGameMetrics) {
        metrics.totalWinPow  = 0;
        metrics.totalBet     = 0;
        metrics.rounds       = 0;
        metrics.stdev        = 0;
        metrics.rtp          = 0;
        metrics.totalWin     = 0;
        metrics.roundWin     = 0;
        metrics.winCount     = 0;
        metrics.triggerCount = 0;
    }
}

/**
 * For calculating game's overall stats
 */
export class BaseGameOverallMetricsHandler extends AbstractBaseGameMetricsHandler {
    getTotalBet(metrics: BaseGameMetrics) {
        return metrics.totalBet;
    }

    getTotalRoundCount(metrics: BaseGameMetrics) {
        return metrics.rounds;
    }
}

/**
 * For calculating each action's metrics
 */
export class BaseGameActionMetricsHandler extends AbstractBaseGameMetricsHandler {
    constructor(readonly action: string, readonly id: string, readonly parent: BaseGameMetricsManager) {
        super(id, parent);
    }

    canHandleMetric(action: string, result: BaseGamePlayStatsResult) {
        return this.action === action;
    }

    /**
     * Action total bet usually will contain only bets made durring that action
     * that value isn't correct when calculating RTP
     *
     * @param {BaseGameMetrics} metrics
     */
    getTotalBet(metrics: BaseGameMetrics) {
        return this.parent.getTotalBet();
    }

    getTotalRoundCount(metrics: BaseGameMetrics) {
        return this.parent.getTotalRoundCount();
    }
}