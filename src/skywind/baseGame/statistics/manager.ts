import {
    BaseGamePlayResponse,
    BaseGameAction,
    BaseGame,
    BaseGameStatsAdapter
} from "../index";

import {
    BaseGameOverallMetricsHandler
} from "./handlers";

import {
    BaseGameMetrics,
    BaseGameMetricsHandler,
    BaseGameMetricsManager,
    BaseGameStatsOutputFeature,
    BaseGameActionMetricsDictionary,
    BaseGamePlayStatsResult
} from "./definitions";

import * as _ from "lodash";

/**
 * Used for containing metric handling logic in single place
 *
 * Game will create metrics manager when its InitGame decorator is called
 */
export class BaseGameMetricsManagerImpl implements BaseGameMetricsManager {
    protected actionMetrics: BaseGameActionMetricsDictionary = {};
    protected gameMetricsHandler: BaseGameMetricsHandler;
    protected actionMetricHandlers: {
        [key: string]: BaseGameMetricsHandler;
    } = {};

    protected statsAdapter: BaseGameStatsAdapter;

    constructor(game: BaseGame) {
        this.statsAdapter = game.config.statsAdapter;

        this.gameMetricsHandler = new BaseGameOverallMetricsHandler(game.gameId, this);
        this.actionMetricHandlers[game.gameId] = this.gameMetricsHandler;

        _.forIn(game.config.gameActions, (action: BaseGameAction) => {
            action.registerMetrics(this);
        });
    }

    registerActionMetricHandler(handler: BaseGameMetricsHandler) {
        this.actionMetricHandlers[handler.id] = handler;
    }

    /**
     * Same as in slot game handlers, actual metrics values are stored
     * outside handler, that allows for easier property addition to
     * metric
     *
     * @param {BaseGameMetricsHandler} handler
     */
    getHandlerMetric(handler: BaseGameMetricsHandler) {
        const id = handler.id;

        if (!this.actionMetrics[id]) {
            this.actionMetrics[id] = handler.initMetric();
        }

        return this.actionMetrics[id];
    }

    handleResult(action: string, result: BaseGamePlayResponse) {
        const metricResult: BaseGamePlayStatsResult = this.statsAdapter.transformPlayResult(result);

        _.forOwn(this.actionMetricHandlers, handler => {
            handler.handleMetrics(this.getHandlerMetric(handler), action, metricResult);
        });
    }

    /**
     * In multi worker environment workers are sending their results to master
     * that will be aggregated to single result set
     *
     * @param {BaseGameActionMetricsDictionary} metrics
     */
    aggregateMetrics(metrics: BaseGameActionMetricsDictionary) {
        _.forOwn(metrics, (metric, id) => {
            if (this.actionMetricHandlers[id]) {
                const sinkMetric = this.getHandlerMetric(this.actionMetricHandlers[id]);
                this.actionMetricHandlers[id].aggregateMetrics(sinkMetric, metric);
            }
        });
    }

    /**
     * When working in multi worker environment results must be sent to
     * master for aggregation
     *
     * @return {BaseGameActionMetricsDictionary}
     */
    getRawMetrics(): BaseGameActionMetricsDictionary {
        return this.actionMetrics;
    }

    resetMetrics() {
        _.forOwn(this.actionMetricHandlers, (handler: BaseGameMetricsHandler) => {
            const metric = this.getHandlerMetric(handler);
            handler.resetMetrics(metric);
        });
    }

    /**
     * For getting total bet in action handlers, their total bet value equals
     * bet amount that was done during that action round, as a result RTP would
     * not be calculated correctly
     *
     * @return {number}
     */
    getTotalBet(): number {
        const metric = this.getHandlerMetric(this.gameMetricsHandler);
        return metric.totalBet;
    }

    getTotalRoundCount(): number {
        const metric = this.getHandlerMetric(this.gameMetricsHandler);
        return metric.rounds;
    }

    getOutputFeatures(): Array<BaseGameStatsOutputFeature> {
        const features: Array<BaseGameStatsOutputFeature> = [];

        _.forOwn(this.actionMetricHandlers, handler => {
            features.push(handler.getOutputFeaturesList(this.getHandlerMetric(handler)));
        });

        return features;
    }
}