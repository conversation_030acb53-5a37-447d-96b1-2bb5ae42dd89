import {
    BalanceResponse, BaseRequest, ContextUpdate, GameContext, GameFlowInfo, GameInitRequest, GamePlayRequest,
    GameStateInfoRequest, JackpotResults, LogLevel, PushService, RandomGenerator, SomeGame, TickersResponse
} from "./definition";
import { GameContextPersistencePolicy } from "./index";

/**
 *  Game stage.
 */
export interface Stage<SET, CTX extends GameContext, SCTX extends GameContext> {
    /**
     * Initialize game stage
     * Method should return void if it doesn't want to process the request  ( see chain of responsibility pattern)
     */
    init(flow: StageFlow<GameInitRequest, SET, CTX, SCTX>): Promise<any>;

    /**
     * Process play request
     * Method should return void if it doesn't want to process the request  ( see chain of responsibility pattern)
     */
    play(flow: StageFlow<GamePlayRequest, SET, CTX, SCTX>): Promise<any>;

    /**
     * Process info request
     * Method should return void if it doesn't want to process the request  ( see chain of responsibility pattern)
     */
    stateInfo(flow: StageFlow<GameStateInfoRequest, SET, CTX, SCTX>): Promise<any>;

    /**
     * MultiStageGame notify that it has finished processing stage and update context;
     * Game stage should cleanup resources if it needs to.
     *
     * The method helps to process game stage fail over and possible duplicate request processing.
     * If a stage works with its own db(or other resource) it should store some flow context with the corresponding
     * flowId. Every time it gets a new request it should check if it has been processed. MultiStageGame garantees that
     * it will invoke finalize methods after making all changes in context.
     *
     * @param flowId
     */
    finalize(flow: StageFlow<BaseRequest, SET, CTX, SCTX>): Promise<void>;

    /**
     * This method is invoked in case if the game has scheduled an internal event through
     * PushService.scheduleInternalEvent
     */
    internalEvent?(flow: StageFlow<GamePlayRequest, SET, CTX, SCTX>): Promise<any>;
}

export type SomeStage = Stage<any, any, any>;

export interface StageFlow<REQ, ST, CTX extends GameContext, SCTX extends GameContext> {

    /**
     * Unique flow id.
     *
     * This value helps a stage implementation to understand if it has processed this request already
     */
    readonly flowId: string;

    /**
     * Incoming game request
     */
    readonly request: REQ;
    /**
     * Game identifier
     */
    readonly info: GameFlowInfo;
    /**
     * Random number generator
     */
    readonly rng: RandomGenerator;

    /**
     *  Different game settings, limits.
     */
    readonly settings: ST;

    /**
     * Push service which can be used for player notifications.
     *
     * It's supported only for the games with bidirectional transport communication
     */
    readonly pushService: PushService;

    /**
     * This method returns player balance from wallet system
     *
     * If the game use coins for play, it should provide coinsRate value for conversion
     *
     * @param coinsRate coinsRate
     */
    readonly balance: Promise<BalanceResponse>;

    /**
     * Get current game context
     */
    readonly gameContext: CTX;

    /**
     * Stage context
     */
    readonly context: SCTX;

    /**
     * Is it possible to process cheats
     */
    readonly allowCheating?: boolean;

    /**
     * Is the current request is the repeat
     */
    readonly repeat: boolean;

    /**
     *  How much player wins in the game
     */
    readonly winAmount?: number;

    /**
     *  How much player bets in the game
     */
    readonly totalBet?: number;

    /**
     * Stage can setup it's own persistent policy.
     * The resulting game policy will be the max value of all game stages
     */
    persistencePolicy: GameContextPersistencePolicy;

    /**
     * Gets current values of jackpot tickers in player's currency.
     */
    jackpotTickers(): Promise<TickersResponse>;

    /**
     * Gets the last jackpot win result.
     */
    jackpotResult(): Promise<JackpotResults>;

    log(level: LogLevel, message: any, ...optionalParams: any[]): void ;

    /**
     * Exchange money from baseCurrency to targetCurrency
     *
     * The parameter baseCurrency is optional, default value is the player currency
     */
    exchange(amount: number, targetCurrency: string, baseCurrency?: string): number;

    deferredUpdate(update: ContextUpdate<SCTX>): void;
}

export type SomeStageFlow = StageFlow<any, any, any, any>;

export interface MultiStageGame extends SomeGame {
}

