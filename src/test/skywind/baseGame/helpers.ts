import {
    BaseGameActionImpl,
    BaseGamePlayResponse,
    BaseGamePlayFlow,
    BaseGameContext,
    BaseGame,
    SomeGameFlow,
    BaseRequest
} from "../../../index";

export class SomeAction extends BaseGameActionImpl {
    constructor(actionName: string, readonly win: number) {
        super(actionName);
    }

    async play(game: BaseGame, flow: BaseGamePlayFlow, context: BaseGameContext,
        allowCheating?: boolean): Promise<BaseGamePlayResponse> {
        return <BaseGamePlayResponse>{
            totalWin: this.win,
            roundEnded: true,
            totalBet: this.win + 1
        };
    }
}

const nop = (): any => {
    throw new Error("Should not have been called");
};

export function createFlow(type: string) {
    const request = <BaseRequest>{
        request       : type,
        name          : "name",
        gameId        : "gameId",
        deviceId      : "deviceId",
        startGameToken: "startGameToken",
        requestId     : 1,
        gameSession   : ""
    };

    const settings = {
        something1 : 1,
        something2 : 2
    };

    return <SomeGameFlow<BaseRequest>>{
        request: (): BaseRequest => {
            return request;
        },
        info: nop,
        rng: nop,
        settings: (): any => settings,
        pushService: nop,
        gameContext: async (): Promise<BaseGameContext> => {
            return <BaseGameContext>{};
        },
        persistencePolicy: nop,
        setPersistencePolicy: nop,
        getBalance: nop,
        payment: nop,
        storeHistory: nop,
        updateGameContext: (): any => {},
        update: (): any => {},
        updateMiniGame: nop,
        log: nop,
        exchange: nop,
        winJackpot: nop,
        jackpotTickers: nop,
        jackpotResult: nop,
        deferredUpdate: nop,
        getFreeBets: nop
    };
}