import { expect, should, use } from "chai";
use(require("chai-as-promised"));
use(should);

import {
    BaseGameConfig,
    BaseGameImpl,
    BaseGame,
    SomeGameFlow,
    BaseRequest
} from "../../../index";

import {
    SomeAction,
    createFlow
} from "./helpers";

import statsConfig from "../../../skywind/baseGame/statistics/config";

describe("manager", () => {
    before(() => {
        statsConfig.statistics.logStatistics = true;
    });

    after(() => {
        statsConfig.statistics.logStatistics = false;
    });

    let config: BaseGameConfig;
    let game: BaseGame;

    beforeEach(() => {
        config = <BaseGameConfig>{
            gameId: "gameId",
            requiredSettings: ["something1", "something2"],
            gameActions: {
                play0 : new SomeAction("play0", 0),
                play1 : new SomeAction("play1", 1),
                play2 : new SomeAction("play2", 2)
            }
        };

        game = new BaseGameImpl(config);
    });

    it("should compute stats correctly", async () => {
        let flow: SomeGameFlow<BaseRequest> = createFlow("play1");
        await game.play(flow);

        flow = createFlow("play1");
        await game.play(flow);

        flow = createFlow("play2");
        await game.play(flow);

        flow = createFlow("play2");
        await game.play(flow);

        flow = createFlow("play0");
        await game.play(flow);

        const features = game.statsManager.getOutputFeatures();

        const stdev = Math.sqrt(
            (Math.pow(1 - 1.2, 2) + Math.pow(1 - 1.2, 2) + Math.pow(2 - 1.2, 2) + Math.pow(2 - 1.2, 2) + Math.pow(0 - 1.2, 2))
            / (5 - 1)
        );

        expect(features.length).to.be.equal(4);
        expect(features[0]).to.be.deep.equal({
            id : "gameId",
            items : [{
                title : "Rounds", value : 5
            }, {
                title : "RTP", value : 6 / 11
            }, {
                title : "Hit Rate", value : 4 / 5
            }, {
                title : "Total Bet", value : (1 + 1) + (1 + 1) + (2 + 1) + (2 + 1) + (0 + 1)
            }, {
                title : "Total Win", value : (1 + 1 + 2 + 2 + 0)
            }, {
                title : "Total Win POW", value : 1 + 1 + 2 * 2 + 2 * 2,
            }, {
                title : "Win Count", value : 4
            }, {
                title : "Average Win", value : 6 / 5
            }, {
                title : "Average Positive Win", value : 6 / 4
            }, {
                title : "1 in X rounds", value : 1
            }, {
                title : "Trigger count", value : 5
            }, {
                title : "STDEV", value: stdev
            }]
        });

        expect(features[1]).to.be.deep.equal({
            id : "play0",
            items : [{
                title : "Rounds", value : 1
            }, {
                title : "RTP", value : 0
            }, {
                title : "Hit Rate", value : 0
            }, {
                title : "Total Bet", value : 11
            }, {
                title : "Total Win", value : 0
            }, {
                title : "Total Win POW", value : 0,
            }, {
                title : "Win Count", value : 0
            }, {
                title : "Average Win", value : 0
            }, {
                title : "Average Positive Win", value : 0
            }, {
                title : "1 in X rounds", value : 5 / 1
            }, {
                title : "Trigger count", value : 1
            }, {
                title : "STDEV", value: NaN
            }]
        });

        expect(features[2]).to.be.deep.equal({
            id : "play1",
            items : [{
                title : "Rounds", value : 2
            }, {
                title : "RTP", value : 2 / 11
            }, {
                title : "Hit Rate", value : 2 / 2
            }, {
                title : "Total Bet", value : 11
            }, {
                title : "Total Win", value : 1 + 1
            }, {
                title : "Total Win POW", value : 1 + 1
            }, {
                title : "Win Count", value : 2
            }, {
                title : "Average Win", value : 2 / 2
            }, {
                title : "Average Positive Win", value : 2 / 2
            }, {
                title : "1 in X rounds", value : 5 / 2
            }, {
                title : "Trigger count", value : 2
            }, {
                title : "STDEV", value: 0
            }]
        });

        expect(features[3]).to.be.deep.equal({
            id : "play2",
            items : [{
                title : "Rounds", value : 2
            }, {
                title : "RTP", value : 4 / 11
            }, {
                title : "Hit Rate", value : 2 / 2
            }, {
                title : "Total Bet", value : 11
            }, {
                title : "Total Win", value : 2 + 2
            }, {
                title : "Total Win POW", value : 2 * 2 + 2 * 2
            }, {
                title : "Win Count", value : 2
            }, {
                title : "Average Win", value : 4 / 2
            }, {
                title : "Average Positive Win", value : 4 / 2
            }, {
                title : "1 in X rounds", value : 5 / 2
            }, {
                title : "Trigger count", value : 2
            }, {
                title : "STDEV", value: 0
            }]
        });
    });

    it("should be able to combine game stats", async () => {
        let flow: SomeGameFlow<BaseRequest> = createFlow("play1");
        await game.play(flow);

        flow = createFlow("play1");
        await game.play(flow);

        flow = createFlow("play2");
        await game.play(flow);

        flow = createFlow("play2");
        await game.play(flow);

        flow = createFlow("play0");
        await game.play(flow);

        const game2 = new BaseGameImpl(config);

        game2.statsManager.aggregateMetrics(game.statsManager.getRawMetrics());

        const features = game2.statsManager.getOutputFeatures();

        const stdev = Math.sqrt(
            (Math.pow(1 - 1.2, 2) + Math.pow(1 - 1.2, 2) + Math.pow(2 - 1.2, 2) + Math.pow(2 - 1.2, 2) + Math.pow(0 - 1.2, 2))
            / (5 - 1)
        );

        expect(features.length).to.be.equal(4);
        expect(features[0]).to.be.deep.equal({
            id : "gameId",
            items : [{
                title : "Rounds", value : 5
            }, {
                title : "RTP", value : 6 / 11
            }, {
                title : "Hit Rate", value : 4 / 5
            }, {
                title : "Total Bet", value : (1 + 1) + (1 + 1) + (2 + 1) + (2 + 1) + (0 + 1)
            }, {
                title : "Total Win", value : (1 + 1 + 2 + 2 + 0)
            }, {
                title : "Total Win POW", value : 1 + 1 + 2 * 2 + 2 * 2,
            }, {
                title : "Win Count", value : 4
            }, {
                title : "Average Win", value : 6 / 5
            }, {
                title : "Average Positive Win", value : 6 / 4
            }, {
                title : "1 in X rounds", value : 1
            }, {
                title : "Trigger count", value : 5
            }, {
                title : "STDEV", value: stdev
            }]
        });

        expect(features[1]).to.be.deep.equal({
            id : "play0",
            items : [{
                title : "Rounds", value : 1
            }, {
                title : "RTP", value : 0
            }, {
                title : "Hit Rate", value : 0
            }, {
                title : "Total Bet", value : 11
            }, {
                title : "Total Win", value : 0
            }, {
                title : "Total Win POW", value : 0,
            }, {
                title : "Win Count", value : 0
            }, {
                title : "Average Win", value : 0
            }, {
                title : "Average Positive Win", value : 0
            }, {
                title : "1 in X rounds", value : 5 / 1
            }, {
                title : "Trigger count", value : 1
            }, {
                title : "STDEV", value: NaN
            }]
        });

        expect(features[2]).to.be.deep.equal({
            id : "play1",
            items : [{
                title : "Rounds", value : 2
            }, {
                title : "RTP", value : 2 / 11
            }, {
                title : "Hit Rate", value : 2 / 2
            }, {
                title : "Total Bet", value : 11
            }, {
                title : "Total Win", value : 1 + 1
            }, {
                title : "Total Win POW", value : 1 + 1
            }, {
                title : "Win Count", value : 2
            }, {
                title : "Average Win", value : 2 / 2
            }, {
                title : "Average Positive Win", value : 2 / 2
            }, {
                title : "1 in X rounds", value : 5 / 2
            }, {
                title : "Trigger count", value : 2
            }, {
                title : "STDEV", value: 0
            }]
        });

        expect(features[3]).to.be.deep.equal({
            id : "play2",
            items : [{
                title : "Rounds", value : 2
            }, {
                title : "RTP", value : 4 / 11
            }, {
                title : "Hit Rate", value : 2 / 2
            }, {
                title : "Total Bet", value : 11
            }, {
                title : "Total Win", value : 2 + 2
            }, {
                title : "Total Win POW", value : 2 * 2 + 2 * 2
            }, {
                title : "Win Count", value : 2
            }, {
                title : "Average Win", value : 4 / 2
            }, {
                title : "Average Positive Win", value : 4 / 2
            }, {
                title : "1 in X rounds", value : 5 / 2
            }, {
                title : "Trigger count", value : 2
            }, {
                title : "STDEV", value: 0
            }]
        });
    });

    it("should return empty results after stats manager reset", async () => {
        let flow: SomeGameFlow<BaseRequest> = createFlow("play1");
        await game.play(flow);

        flow = createFlow("play2");
        await game.play(flow);

        flow = createFlow("play0");
        await game.play(flow);

        game.statsManager.resetMetrics();
        const features = game.statsManager.getOutputFeatures();

        const emptyItems = [{
            title : "Rounds", value : 0
        }, {
            title : "RTP", value : NaN
        }, {
            title : "Hit Rate", value : NaN
        }, {
            title : "Total Bet", value : 0
        }, {
            title : "Total Win", value : 0
        }, {
            title : "Total Win POW", value : 0
        }, {
            title : "Win Count", value : 0
        }, {
            title : "Average Win", value : NaN
        }, {
            title : "Average Positive Win", value : 0
        }, {
            title : "1 in X rounds", value : 0
        }, {
            title : "Trigger count", value : 0
        }, {
            title : "STDEV", value: NaN
        }];

        expect(features.length).to.be.equal(4);
        expect(features[0]).to.be.deep.equal({
            id   : "gameId",
            items: emptyItems
        });

        expect(features[1]).to.be.deep.equal({
            id   : "play0",
            items: emptyItems
        });

        expect(features[2]).to.be.deep.equal({
            id   : "play1",
            items: emptyItems
        });

        expect(features[3]).to.be.deep.equal({
            id   : "play2",
            items: emptyItems
        });
    });
});