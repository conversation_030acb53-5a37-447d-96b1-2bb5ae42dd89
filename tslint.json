{"rules": {"class-name": true, "comment-format": [true, "check-space"], "indent": [true, "spaces"], "no-duplicate-variable": true, "no-eval": true, "no-internal-module": true, "no-trailing-whitespace": true, "no-var-keyword": true, "one-line": [true, "check-open-brace", "check-whitespace"], "quotemark": [false], "semicolon": [true, "always"], "triple-equals": [true, "allow-null-check"], "typedef-whitespace": [true, {"call-signature": "nospace", "index-signature": "nospace", "parameter": "nospace", "property-declaration": "nospace", "variable-declaration": "nospace"}], "variable-name": [true, "ban-keywords"], "whitespace": [true, "check-branch", "check-decl", "check-operator", "check-separator", "check-type"]}}